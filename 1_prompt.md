# 📝 Prompt: Integrate Firebase for Cloud Sync

## 1. Objective

To set up a new Firebase project and integrate the required Firebase SDKs (Authentication and Firestore) into the Android application, preparing the codebase for cloud features.

## 2. Detailed Implementation Plan

This task involves configuration in both the Firebase console and the Android Studio project.

### Task 2.1: Create and Configure the Firebase Project(Done Manually!)

1.  **Create a Firebase Project:** Go to the [Firebase Console](https://console.firebase.google.com/) and create a new project for this application.
2.  **Register the Android App:** Within the new project, register our Android app. You will need the app's package name (`com.example.uhabits_99` or similar, found in your `build.gradle.kts` file).
3.  **Download Config File:** Download the generated `google-services.json` file.
4.  **Enable Services:** In the Firebase console, navigate to the "Build" section and enable both **Authentication** (with the "Email/Password" sign-in method) and **Firestore Database**. Create the Firestore database in a test location for now.

### Task 2.2: Integrate the Firebase SDKs into the Android Project

1.  **Add Config File:** Place the `google-services.json` file you downloaded into the `app` directory of your Android project.
2.  **Update Gradle Files:**
    * **Project-level `build.gradle.kts`:** Add the Google Services plugin dependency.
    * **App-level `build.gradle.kts`:**
        * Apply the Google Services plugin (`id("com.google.gms.google-services")`).
        * Import the Firebase Bill of Materials (BOM) to manage SDK versions.
        * Add the specific dependencies for **Firebase Authentication** (`firebase-auth-ktx`) and **Cloud Firestore** (`firebase-firestore-ktx`).

## 3. Verification / Testing Section

To confirm that Firebase is integrated correctly, perform the following checks:

* **Test Case 1: Successful Build:**
    * After adding the new dependencies and the JSON file, sync your Gradle files.
    * Build and run the app on an emulator or physical device.
    * **Expected Outcome:** The app must compile and launch successfully without any build errors or runtime crashes related to Firebase initialization.

* **Test Case 2: Firestore Instance:**
    * Temporarily, in your `MainActivity.kt`'s `onCreate` method, add the following line of code:
        ```kotlin
        val db = com.google.firebase.firestore.ktx.firestore
        android.util.Log.d("FirebaseCheck", "Firestore instance: $db")
        ```
    * Run the app and check the Logcat window (filtered by "FirebaseCheck").
    * **Expected Outcome:** The app should not crash, and you should see a log message confirming that the Firestore instance was successfully retrieved. This proves the SDK is correctly configured and communicating with your Firebase project. You can remove the test code after this verification.

## 4. Mandatory Development Guidelines

**These practices must be followed during all phases of development—planning, implementation, and review.**

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project 
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.