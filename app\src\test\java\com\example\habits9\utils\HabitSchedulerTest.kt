package com.example.habits9.utils

import com.example.habits9.data.DayOfWeek
import com.example.habits9.data.EnhancedFrequency
import com.example.habits9.data.FrequencyType
import com.example.habits9.data.Habit
import org.junit.Assert.assertFalse
import org.junit.Assert.assertTrue
import org.junit.Assert.assertEquals
import org.junit.Test
import java.time.LocalDate
import java.time.ZoneId

class HabitSchedulerTest {

    // Reference date for testing (2023-01-01 is a Sunday)
    private val referenceDate = LocalDate.of(2023, 1, 1)
    
    // Creation timestamp for January 1, 2023 (in milliseconds)
    private val creationTimestamp = referenceDate.toEpochDay() * 24 * 60 * 60 * 1000

    @Test
    fun `test daily habit with repeatsEvery = 1`() {
        // A habit that repeats every day
        val frequency = EnhancedFrequency(
            type = FrequencyType.DAILY,
            repeatsEvery = 1
        )

        // Should be scheduled for every day
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate, creationTimestamp))
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(1), creationTimestamp))
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(2), creationTimestamp))
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(10), creationTimestamp))
    }

    @Test
    fun `test daily habit with repeatsEvery = 3`() {
        // A habit that repeats every 3 days
        val frequency = EnhancedFrequency(
            type = FrequencyType.DAILY,
            repeatsEvery = 3
        )

        // Should be scheduled for day 0, 3, 6, etc.
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate, creationTimestamp))
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(1), creationTimestamp))
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(2), creationTimestamp))
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(3), creationTimestamp))
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(4), creationTimestamp))
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(5), creationTimestamp))
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(6), creationTimestamp))
    }

    @Test
    fun `test weekly habit with repeatsEvery = 1 on specific days`() {
        // A habit that repeats every week on Monday and Thursday
        val frequency = EnhancedFrequency(
            type = FrequencyType.WEEKLY,
            repeatsEvery = 1,
            daysOfWeek = listOf(DayOfWeek.MONDAY, DayOfWeek.THURSDAY)
        )

        // January 2023: 1(Sun), 2(Mon), 3(Tue), 4(Wed), 5(Thu), 6(Fri), 7(Sat)
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate, creationTimestamp)) // Sunday
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(1), creationTimestamp)) // Monday
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(2), creationTimestamp)) // Tuesday
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(3), creationTimestamp)) // Wednesday
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(4), creationTimestamp)) // Thursday
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(5), creationTimestamp)) // Friday
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(6), creationTimestamp)) // Saturday
        
        // Next week should also have Monday and Thursday
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(8), creationTimestamp)) // Monday
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(11), creationTimestamp)) // Thursday
    }

    @Test
    fun `test weekly habit with repeatsEvery = 2 on specific days`() {
        // A habit that repeats every 2 weeks on Wednesday and Sunday
        val frequency = EnhancedFrequency(
            type = FrequencyType.WEEKLY,
            repeatsEvery = 2,
            daysOfWeek = listOf(DayOfWeek.WEDNESDAY, DayOfWeek.SUNDAY)
        )

        // First week (should be active)
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate, creationTimestamp)) // Sunday
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(1), creationTimestamp)) // Monday
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(2), creationTimestamp)) // Tuesday
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(3), creationTimestamp)) // Wednesday
        
        // Second week (should be inactive)
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(7), creationTimestamp)) // Sunday
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(10), creationTimestamp)) // Wednesday
        
        // Third week (should be active again)
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(14), creationTimestamp)) // Sunday
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate.plusDays(17), creationTimestamp)) // Wednesday
    }

    @Test
    fun `test monthly habit with specific day of month`() {
        // A habit that repeats on the 15th of every month
        val frequency = EnhancedFrequency(
            type = FrequencyType.MONTHLY,
            repeatsEvery = 1,
            dayOfMonth = 15
        )

        // Should be scheduled only on the 15th of each month
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate, creationTimestamp)) // Jan 1
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 1, 15), creationTimestamp)) // Jan 15
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 1, 16), creationTimestamp)) // Jan 16
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 2, 15), creationTimestamp)) // Feb 15
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 3, 15), creationTimestamp)) // Mar 15
    }

    @Test
    fun `test monthly habit with repeatsEvery = 2 and specific day`() {
        // A habit that repeats on the 10th of every 2 months
        val frequency = EnhancedFrequency(
            type = FrequencyType.MONTHLY,
            repeatsEvery = 2,
            dayOfMonth = 10
        )

        // Should be scheduled on the 10th of January, March, May, etc.
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate, creationTimestamp)) // Jan 1
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 1, 10), creationTimestamp)) // Jan 10
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 2, 10), creationTimestamp)) // Feb 10
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 3, 10), creationTimestamp)) // Mar 10
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 4, 10), creationTimestamp)) // Apr 10
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 5, 10), creationTimestamp)) // May 10
    }

    @Test
    fun `test monthly habit with specific week and day of week`() {
        // A habit that repeats on the third Tuesday of every month
        val frequency = EnhancedFrequency(
            type = FrequencyType.MONTHLY,
            repeatsEvery = 1,
            weekOfMonth = 3,
            dayOfWeekInMonth = DayOfWeek.TUESDAY
        )

        // Third Tuesday of January 2023 is January 17
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, referenceDate, creationTimestamp)) // Jan 1
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 1, 10), creationTimestamp)) // Jan 10
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 1, 17), creationTimestamp)) // Jan 17 (3rd Tuesday)
        assertFalse(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 1, 24), creationTimestamp)) // Jan 24 (4th Tuesday)
        
        // Third Tuesday of February 2023 is February 21
        assertTrue(HabitScheduler.isScheduledWithFrequency(frequency, LocalDate.of(2023, 2, 21), creationTimestamp)) // Feb 21 (3rd Tuesday)
    }

    @Test
    fun `test isHabitScheduled with mock habit`() {
        // Create a mock habit with weekly frequency (every 2 weeks on Wednesday and Sunday)
        val habit = Habit(
            id = 1,
            name = "Test Habit",
            creationDate = creationTimestamp,
            frequencyType = "WEEKLY",
            repeatsEvery = 2,
            daysOfWeek = "3,7" // Wednesday and Sunday
        )

        // First week (should be active)
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate)) // Sunday
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(1))) // Monday
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(2))) // Tuesday
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(3))) // Wednesday

        // Second week (should be inactive)
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(7))) // Sunday
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(10))) // Wednesday

        // Third week (should be active again)
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(14))) // Sunday
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(17))) // Wednesday
    }

    @Test
    fun `test habit scheduled only for Tuesdays`() {
        // Test Case 1 from prompt: Create a habit scheduled only for Tuesdays
        val habit = Habit(
            id = 2,
            name = "Tuesday Only Habit",
            creationDate = creationTimestamp,
            frequencyType = "WEEKLY",
            repeatsEvery = 1,
            daysOfWeek = "2" // Tuesday only
        )

        // Check all days of the week - only Tuesday should return true
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate)) // Sunday
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(1))) // Monday
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(2))) // Tuesday
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(3))) // Wednesday
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(4))) // Thursday
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(5))) // Friday
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(6))) // Saturday
    }

    @Test
    fun `test habit scheduled for Monday Thursday Saturday - bug reproduction`() {
        // Test Case from bug report: Create a habit scheduled for Monday, Thursday, and Saturday
        val habit = Habit(
            id = 3,
            name = "M,T,S Habit",
            creationDate = creationTimestamp,
            frequencyType = "WEEKLY",
            repeatsEvery = 1,
            daysOfWeek = "1,4,6" // Monday, Thursday, Saturday
        )

        // Check all days of the week
        // Expected: Monday, Thursday, Saturday should return TRUE (scheduled)
        // Expected: Sunday, Tuesday, Wednesday, Friday should return FALSE (not scheduled)
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate)) // Sunday - should be FALSE
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(1))) // Monday - should be TRUE
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(2))) // Tuesday - should be FALSE
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(3))) // Wednesday - should be FALSE
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(4))) // Thursday - should be TRUE
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(5))) // Friday - should be FALSE
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(6))) // Saturday - should be TRUE
    }

    @Test
    fun `test timestamp normalization consistency`() {
        // Test to verify that timestamp normalization is consistent between UI and ViewModel
        val habit = Habit(
            id = 3,
            name = "Daily Test Habit",
            creationDate = creationTimestamp,
            frequencyType = "DAILY",
            repeatsEvery = 1
        )

        // Simulate timestamps as they would come from weekInfo.timestamps
        val today = LocalDate.now()
        val originalTimestamp = today.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()

        // Simulate UI normalization (from HomeScreen.kt)
        val dayLength = 24 * 60 * 60 * 1000L
        val uiNormalizedTimestamp = (originalTimestamp / dayLength) * dayLength

        // Simulate ViewModel normalization (from calculateScheduledDays)
        val viewModelNormalizedTimestamp = (originalTimestamp / dayLength) * dayLength

        // They should be equal
        assertEquals("Timestamp normalization should be consistent", uiNormalizedTimestamp, viewModelNormalizedTimestamp)

        // Both should result in the same date
        val uiDate = LocalDate.ofEpochDay(uiNormalizedTimestamp / dayLength)
        val viewModelDate = LocalDate.ofEpochDay(viewModelNormalizedTimestamp / dayLength)
        assertEquals("Dates should be equal", uiDate, viewModelDate)

        // Both should return the same scheduling result
        val uiSchedulingResult = HabitScheduler.isHabitScheduled(habit, uiDate)
        val viewModelSchedulingResult = HabitScheduler.isHabitScheduled(habit, viewModelDate)
        assertEquals("Scheduling results should be equal", uiSchedulingResult, viewModelSchedulingResult)
    }

    @Test
    fun `test habit scheduled for Mon Wed Fri`() {
        // Test Case 1 from prompt: Create a habit scheduled on Mon/Wed/Fri
        val habit = Habit(
            id = 3,
            name = "Mon Wed Fri Habit",
            creationDate = creationTimestamp,
            frequencyType = "WEEKLY",
            repeatsEvery = 1,
            daysOfWeek = "1,3,5" // Monday, Wednesday, Friday
        )

        // Check all days of the week
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate)) // Sunday - should be grayed out
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(1))) // Monday - active
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(2))) // Tuesday - should be grayed out
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(3))) // Wednesday - active
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(4))) // Thursday - should be grayed out
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(5))) // Friday - active
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(6))) // Saturday - should be grayed out
    }

    @Test
    fun `test habit repeats every 3 days`() {
        // Test Case 2 from prompt: Create a habit that repeats every 3 days
        val habit = Habit(
            id = 4,
            name = "Every 3 Days Habit",
            creationDate = creationTimestamp,
            frequencyType = "DAILY",
            repeatsEvery = 3
        )

        // Should be scheduled on day 1, 4, 7, 10, etc.
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate)) // Day 1
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(1))) // Day 2
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(2))) // Day 3
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(3))) // Day 4
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(4))) // Day 5
        assertFalse(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(5))) // Day 6
        assertTrue(HabitScheduler.isHabitScheduled(habit, referenceDate.plusDays(6))) // Day 7
    }

    @Test
    fun `test monthly habit on 10th and 20th`() {
        // Test Case 3 from prompt: Create a habit scheduled for the 10th and 20th of every month
        // Note: This would require two separate habits in the current implementation
        // since we can only specify one dayOfMonth per habit
        val habit10th = Habit(
            id = 5,
            name = "10th of Month Habit",
            creationDate = creationTimestamp,
            frequencyType = "MONTHLY",
            repeatsEvery = 1,
            dayOfMonth = 10
        )

        val habit20th = Habit(
            id = 6,
            name = "20th of Month Habit",
            creationDate = creationTimestamp,
            frequencyType = "MONTHLY",
            repeatsEvery = 1,
            dayOfMonth = 20
        )

        // Test for January 2023
        assertTrue(HabitScheduler.isHabitScheduled(habit10th, LocalDate.of(2023, 1, 10)))
        assertFalse(HabitScheduler.isHabitScheduled(habit10th, LocalDate.of(2023, 1, 20)))

        assertFalse(HabitScheduler.isHabitScheduled(habit20th, LocalDate.of(2023, 1, 10)))
        assertTrue(HabitScheduler.isHabitScheduled(habit20th, LocalDate.of(2023, 1, 20)))

        // Test for February 2023
        assertTrue(HabitScheduler.isHabitScheduled(habit10th, LocalDate.of(2023, 2, 10)))
        assertTrue(HabitScheduler.isHabitScheduled(habit20th, LocalDate.of(2023, 2, 20)))
    }
}
